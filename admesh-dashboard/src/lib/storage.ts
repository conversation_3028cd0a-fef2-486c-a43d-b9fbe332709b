// lib/storage.ts
import { storage, auth } from "@/lib/firebase";
import { ref, uploadBytes, getDownloadURL, listAll, getMetadata, deleteObject } from "firebase/storage";

export interface StoredAd {
  id: string;
  platform: string;
  headline: string;
  description: string;
  cta: string;
  content: string;
  media_assets?: Array<{
    url: string;
    type: string;
  }>;
  timestamp: string;
  downloadUrl?: string;
}

export interface AdStorageMetadata {
  prompt: string;
  platforms: string[];
  imageStyle: string;
  timestamp: string;
  totalAds: number;
}

/**
 * Save generated ads to Firebase Storage
 */
export async function saveAdsToStorage(
  ads: any[], 
  metadata: AdStorageMetadata
): Promise<string[]> {
  const user = auth.currentUser;
  if (!user) {
    throw new Error("User must be authenticated to save ads");
  }

  const savedAdIds: string[] = [];
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  
  try {
    // Create a batch folder for this generation
    const batchId = `batch_${timestamp}`;
    
    for (let i = 0; i < ads.length; i++) {
      const ad = ads[i];
      const adId = `ad_${i + 1}_${timestamp}`;
      
      // Prepare ad data
      const adData: StoredAd = {
        id: adId,
        platform: ad.platform || 'unknown',
        headline: ad.headline || '',
        description: ad.description || '',
        cta: ad.cta || '',
        content: ad.content || '',
        media_assets: ad.media_assets || [],
        timestamp: new Date().toISOString()
      };

      // Create storage path: brands/{uid}/ads-generated/{batchId}/{adId}.json
      const adPath = `brands/${user.uid}/ads-generated/${batchId}/${adId}.json`;
      const adRef = ref(storage, adPath);
      
      // Convert ad data to blob
      const adBlob = new Blob([JSON.stringify(adData, null, 2)], {
        type: 'application/json'
      });
      
      // Upload ad data
      await uploadBytes(adRef, adBlob);
      savedAdIds.push(adId);
    }

    // Save batch metadata
    const metadataPath = `brands/${user.uid}/ads-generated/${batchId}/metadata.json`;
    const metadataRef = ref(storage, metadataPath);
    const metadataBlob = new Blob([JSON.stringify({
      ...metadata,
      batchId,
      adIds: savedAdIds,
      savedAt: new Date().toISOString()
    }, null, 2)], {
      type: 'application/json'
    });
    
    await uploadBytes(metadataRef, metadataBlob);
    
    return savedAdIds;
  } catch (error) {
    console.error("Error saving ads to storage:", error);
    throw error;
  }
}

/**
 * Retrieve all saved ads for the current user
 */
export async function getSavedAds(): Promise<{ ads: StoredAd[], batches: any[] }> {
  const user = auth.currentUser;
  if (!user) {
    throw new Error("User must be authenticated to retrieve ads");
  }

  try {
    const userAdsPath = `brands/${user.uid}/ads-generated/`;
    const userAdsRef = ref(storage, userAdsPath);
    
    // List all batch folders
    const batchList = await listAll(userAdsRef);
    const batches: any[] = [];
    const allAds: StoredAd[] = [];

    for (const batchRef of batchList.prefixes) {
      try {
        // Get batch metadata
        const metadataRef = ref(storage, `${batchRef.fullPath}/metadata.json`);
        const metadataUrl = await getDownloadURL(metadataRef);
        const metadataResponse = await fetch(metadataUrl);
        const batchMetadata = await metadataResponse.json();

        // Get all ads in this batch
        const batchAdsList = await listAll(batchRef);
        const batchAds: StoredAd[] = [];

        for (const adRef of batchAdsList.items) {
          if (adRef.name.endsWith('.json') && adRef.name !== 'metadata.json') {
            try {
              const adUrl = await getDownloadURL(adRef);
              const adResponse = await fetch(adUrl);
              const adData = await adResponse.json();
              adData.downloadUrl = adUrl;
              batchAds.push(adData);
              allAds.push(adData);
            } catch (error) {
              console.error(`Error loading ad ${adRef.name}:`, error);
            }
          }
        }

        batches.push({
          ...batchMetadata,
          ads: batchAds,
          folderPath: batchRef.fullPath
        });
      } catch (error) {
        console.error(`Error loading batch ${batchRef.name}:`, error);
      }
    }

    // Sort batches by timestamp (newest first)
    batches.sort((a, b) => new Date(b.savedAt).getTime() - new Date(a.savedAt).getTime());

    return { ads: allAds, batches };
  } catch (error) {
    console.error("Error retrieving saved ads:", error);
    throw error;
  }
}

/**
 * Delete a specific ad batch
 */
export async function deleteAdBatch(batchId: string): Promise<void> {
  const user = auth.currentUser;
  if (!user) {
    throw new Error("User must be authenticated to delete ads");
  }

  try {
    const batchPath = `brands/${user.uid}/ads-generated/${batchId}/`;
    const batchRef = ref(storage, batchPath);
    
    // List all files in the batch
    const batchList = await listAll(batchRef);
    
    // Delete all files in the batch
    const deletePromises = batchList.items.map(item => deleteObject(item));
    await Promise.all(deletePromises);
    
  } catch (error) {
    console.error("Error deleting ad batch:", error);
    throw error;
  }
}

/**
 * Get storage usage statistics
 */
export async function getStorageStats(): Promise<{
  totalAds: number;
  totalBatches: number;
  storageUsed: number;
}> {
  const user = auth.currentUser;
  if (!user) {
    throw new Error("User must be authenticated to get storage stats");
  }

  try {
    const { ads, batches } = await getSavedAds();
    
    // Calculate approximate storage usage
    let storageUsed = 0;
    for (const batch of batches) {
      try {
        const batchRef = ref(storage, batch.folderPath);
        const batchList = await listAll(batchRef);
        
        for (const item of batchList.items) {
          const metadata = await getMetadata(item);
          storageUsed += metadata.size || 0;
        }
      } catch (error) {
        console.error("Error calculating storage for batch:", batch.batchId, error);
      }
    }

    return {
      totalAds: ads.length,
      totalBatches: batches.length,
      storageUsed // in bytes
    };
  } catch (error) {
    console.error("Error getting storage stats:", error);
    return {
      totalAds: 0,
      totalBatches: 0,
      storageUsed: 0
    };
  }
}
