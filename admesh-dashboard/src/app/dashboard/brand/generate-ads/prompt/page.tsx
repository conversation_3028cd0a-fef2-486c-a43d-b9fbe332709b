"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import {
  Sparkles,
  Image as ImageIcon,
  Loader2,
  History,
  Download,
  Copy,
  Eye,
  Palette,
  Zap,
  Rocket,
  Star,
  Heart,
  Fire,
  Wand2,
  Target,
  Settings,
  TrendingUp,
  MessageSquare,
  Clock,
  CheckCircle2,
  ArrowRight,
  Lightbulb,
  Share2,
  RefreshCw,
  Facebook,
  Instagram,
  Linkedin,
  Twitter,
  Youtube,
  Search,
  Music,
  Monitor,
  Briefcase,
  Brush,
  Zap as BoltIcon,
  Minimize,
  Coffee,
  Smartphone,
  Globe,
  Users
} from "lucide-react";
import DashboardFooter from "@/components/DashboardFooter";

interface GeneratedAd {
  id: string;
  type: string;
  content: string;
  headline?: string;
  description?: string;
  cta?: string;
  platform: string;
  format: string;
  media_assets?: Array<{
    id: string;
    type: string;
    url: string;
    storage_path: string;
    metadata: Record<string, any>;
  }>;
  generation_metadata?: Record<string, any>;
}

interface HistoryEntry {
  id: string;
  prompt: string;
  platforms: string[];
  imageStyle: string;
  adsCount: number;
  timestamp: string;
  ads: GeneratedAd[];
}

export default function PromptAdGeneratorPage() {
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedAds, setGeneratedAds] = useState<GeneratedAd[]>([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(["facebook", "instagram", "linkedin", "google", "tiktok"]);
  const [imageStyle, setImageStyle] = useState("creative");
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [activeTab, setActiveTab] = useState("generate");

  const platforms = [
    {
      value: "facebook",
      label: "Facebook",
      icon: Facebook,
      color: "bg-blue-600",
      description: "Social media advertising",
      users: "2.9B users"
    },
    {
      value: "instagram",
      label: "Instagram",
      icon: Instagram,
      color: "bg-gradient-to-r from-purple-500 to-pink-500",
      description: "Visual storytelling",
      users: "2B users"
    },
    {
      value: "google",
      label: "Google Ads",
      icon: Search,
      color: "bg-green-600",
      description: "Search advertising",
      users: "8.5B searches/day"
    },
    {
      value: "linkedin",
      label: "LinkedIn",
      icon: Linkedin,
      color: "bg-blue-700",
      description: "Professional network",
      users: "900M users"
    },
    {
      value: "twitter",
      label: "Twitter/X",
      icon: Twitter,
      color: "bg-gray-900",
      description: "Real-time updates",
      users: "450M users"
    },
    {
      value: "tiktok",
      label: "TikTok",
      icon: Music,
      color: "bg-black",
      description: "Short-form videos",
      users: "1B users"
    },
    {
      value: "youtube",
      label: "YouTube",
      icon: Youtube,
      color: "bg-red-600",
      description: "Video platform",
      users: "2.7B users"
    }
  ];

  const imageStyles = [
    {
      value: "creative",
      label: "Creative & Wild",
      icon: Brush,
      description: "Artistic, bold, and imaginative designs"
    },
    {
      value: "bold",
      label: "Bold & Striking",
      icon: BoltIcon,
      description: "High-contrast, attention-grabbing visuals"
    },
    {
      value: "minimalist",
      label: "Clean & Minimal",
      icon: Minimize,
      description: "Simple, elegant, and focused designs"
    },
    {
      value: "professional",
      label: "Professional",
      icon: Briefcase,
      description: "Corporate, trustworthy, and polished"
    }
  ];

  const promptExamples = [
    {
      text: "Revolutionary AI-powered fitness app that reads your mind and creates perfect workouts",
      category: "Tech & Fitness",
      icon: Smartphone
    },
    {
      text: "Eco-friendly sneakers made from ocean plastic - save the planet with every step",
      category: "Sustainability",
      icon: Globe
    },
    {
      text: "Premium coffee that gives you superpowers - unlock your potential with every sip",
      category: "Food & Beverage",
      icon: Coffee
    },
    {
      text: "Dating app for introverts - find love without the social anxiety",
      category: "Social & Dating",
      icon: Users
    },
    {
      text: "Time-travel vacation packages - visit any era in history safely",
      category: "Travel & Adventure",
      icon: Clock
    },
    {
      text: "Smart home system that anticipates your needs before you know them",
      category: "Smart Home",
      icon: Monitor
    }
  ];

  useEffect(() => {
    loadHistory();
  }, []);

  const generateAds = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt for your ad");
      return;
    }

    if (selectedPlatforms.length === 0) {
      toast.error("Please select at least one platform");
      return;
    }

    setIsGenerating(true);

    try {
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) {
        toast.error("Please log in to generate ads");
        return;
      }

      const token = await getIdToken(auth.currentUser);
      const allGeneratedAds: GeneratedAd[] = [];
      
      // Generate ads for all selected platforms
      for (const platform of selectedPlatforms) {
        try {
          const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';
          const response = await fetch(`${apiBaseUrl}/ad-generation/generate`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            },
            body: JSON.stringify({
              product_name: extractProductName(prompt),
              product_description: prompt,
              target_audience: "General audience",
              ad_type: "multimodal",
              platform: platform,
              tone: "creative",
              keywords: "",
              cta: "Learn More",
              additional_info: "Make it crazy and eye-catching!",
              num_variations: 2,
              // Always generate images for crazy visual ads
              generate_images: true,
              generate_videos: false,
              image_style: imageStyle,
              video_duration: 15,
              brand_colors: [],
              brand_fonts: [],
              use_brand_assets: true
            })
          });

          if (response.ok) {
            const data = await response.json();
            if (data.success && data.ads) {
              allGeneratedAds.push(...data.ads);
            }
          }
        } catch (error) {
          console.error(`Error generating ads for ${platform}:`, error);
        }
      }

      if (allGeneratedAds.length > 0) {
        setGeneratedAds(allGeneratedAds);
        toast.success(`🎉 Generated ${allGeneratedAds.length} crazy ads with images!`);
        
        // Add to history
        const historyEntry: HistoryEntry = {
          id: Date.now().toString(),
          prompt: prompt,
          platforms: selectedPlatforms,
          imageStyle: imageStyle,
          adsCount: allGeneratedAds.length,
          timestamp: new Date().toISOString(),
          ads: allGeneratedAds
        };
        setHistory(prev => [historyEntry, ...prev]);
        setActiveTab("results");
      } else {
        throw new Error("Failed to generate any ads");
      }
    } catch (error) {
      console.error("Error generating ads:", error);
      toast.error(error instanceof Error ? error.message : "Failed to generate ads");
    } finally {
      setIsGenerating(false);
    }
  };

  const extractProductName = (prompt: string): string => {
    const words = prompt.split(' ');
    if (words.length <= 3) return prompt;
    
    const productIndicators = ['for', 'about', 'selling', 'promoting'];
    for (const indicator of productIndicators) {
      const index = words.findIndex(word => word.toLowerCase().includes(indicator));
      if (index > 0) {
        return words.slice(0, index).join(' ');
      }
    }
    
    return words.slice(0, 3).join(' ');
  };

  const loadHistory = async () => {
    try {
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) return;

      const token = await getIdToken(auth.currentUser);
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';
      const response = await fetch(`${apiBaseUrl}/ad-generation/history?limit=20`, {
        headers: {
          "Authorization": `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.history) {
          setHistory(data.history);
        }
      }
    } catch (error) {
      console.error("Error loading history:", error);
    }
  };

  const handlePlatformToggle = (platform: string) => {
    setSelectedPlatforms(prev => 
      prev.includes(platform)
        ? prev.filter(p => p !== platform)
        : [...prev, platform]
    );
  };

  const copyAdContent = (ad: GeneratedAd) => {
    const content = `${ad.headline}\n\n${ad.description}\n\n${ad.cta}`;
    navigator.clipboard.writeText(content);
    toast.success("Ad content copied to clipboard!");
  };

  const loadFromHistory = (entry: HistoryEntry) => {
    setPrompt(entry.prompt);
    setSelectedPlatforms(entry.platforms);
    setImageStyle(entry.imageStyle);
    setGeneratedAds(entry.ads);
    setActiveTab("results");
    toast.success("Loaded from history!");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-indigo-50/30">
      {/* Compact Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="px-4 py-4 sm:px-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 text-white">
                  <Wand2 className="h-5 w-5" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">
                    Prompt Ad Generator
                  </h1>
                  <p className="text-sm text-gray-600">
                    Create stunning visual ads with AI
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  <Sparkles className="h-3 w-3 mr-1" />
                  AI Powered
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowHistory(!showHistory)}
                  className="flex items-center gap-1"
                >
                  <History className="h-3 w-3" />
                  History ({history.length})
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-4 py-4 sm:px-6">
        <div className="max-w-7xl mx-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <div className="flex justify-center">
              <TabsList className="grid w-full max-w-md grid-cols-3 bg-gray-100 p-1 rounded-lg">
                <TabsTrigger
                  value="generate"
                  className="rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm text-sm"
                >
                  <Rocket className="h-3 w-3 mr-1" />
                  Generate
                </TabsTrigger>
                <TabsTrigger
                  value="results"
                  className="rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm text-sm"
                >
                  <Eye className="h-3 w-3 mr-1" />
                  Results ({generatedAds.length})
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm text-sm"
                >
                  <History className="h-3 w-3 mr-1" />
                  History
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="generate" className="space-y-6">
              {/* Prompt Input Card */}
              <Card className="shadow-sm hover:shadow-md transition-all duration-200">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <div className="p-1.5 rounded-md bg-purple-100 text-purple-600">
                      <Lightbulb className="h-4 w-4" />
                    </div>
                    What's Your Product Idea?
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    Describe your product or service. Be creative and specific!
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="prompt" className="text-sm font-medium text-gray-700 mb-1.5 block">
                      Product Description
                    </Label>
                    <Textarea
                      id="prompt"
                      placeholder="Example: A revolutionary AI-powered fitness app that reads your mind and creates perfect workouts..."
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      className="min-h-[100px] text-sm resize-none"
                    />
                    <div className="flex items-center justify-between mt-1.5">
                      <p className="text-xs text-gray-500">
                        Be creative! AI loves imaginative ideas.
                      </p>
                      <span className="text-xs text-gray-400">
                        {prompt.length}/500
                      </span>
                    </div>
                  </div>

                  {/* Example prompts */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <p className="text-sm font-medium text-gray-700">Need inspiration? Try these examples:</p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {promptExamples.map((example, index) => (
                        <div
                          key={index}
                          className="p-4 border border-gray-200 rounded-xl hover:border-purple-300 hover:bg-purple-50/50 cursor-pointer transition-all duration-200 group"
                          onClick={() => setPrompt(example.text)}
                        >
                          <div className="flex items-start gap-3">
                            <example.icon className="h-5 w-5 text-gray-600 mt-0.5" />
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge variant="outline" className="text-xs">
                                  {example.category}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-700 group-hover:text-purple-700 transition-colors">
                                {example.text}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Platform Selection Card */}
              <Card className="shadow-sm hover:shadow-md transition-all duration-200 border-l-4 border-l-blue-500">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                      <Target className="h-5 w-5" />
                    </div>
                    Choose Your Platforms
                  </CardTitle>
                  <p className="text-sm text-gray-600 mt-1">
                    Select where you want your ads to appear. Each platform will get optimized content.
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {platforms.map((platform) => (
                      <div
                        key={platform.value}
                        className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-lg group ${
                          selectedPlatforms.includes(platform.value)
                            ? "border-blue-500 bg-blue-50 shadow-md"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                        onClick={() => handlePlatformToggle(platform.value)}
                      >
                        <div className="flex items-start gap-3">
                          <div className="flex-shrink-0">
                            <Checkbox
                              checked={selectedPlatforms.includes(platform.value)}
                              onChange={() => handlePlatformToggle(platform.value)}
                              className="mt-1"
                            />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <platform.icon className="h-5 w-5 text-gray-600" />
                              <span className="font-semibold text-gray-900">{platform.label}</span>
                            </div>
                            <p className="text-xs text-gray-600 mb-1">{platform.description}</p>
                            <Badge variant="outline" className="text-xs">
                              {platform.users}
                            </Badge>
                          </div>
                        </div>
                        {selectedPlatforms.includes(platform.value) && (
                          <div className="absolute top-2 right-2">
                            <CheckCircle2 className="h-5 w-5 text-blue-600" />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600 flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Selected: {selectedPlatforms.length} platform{selectedPlatforms.length !== 1 ? 's' : ''}
                      {selectedPlatforms.length > 0 && (
                        <span className="text-blue-600 font-medium">
                          → {selectedPlatforms.length * 2} ads will be generated
                        </span>
                      )}
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Image Style Card */}
              <Card className="shadow-sm hover:shadow-md transition-all duration-200 border-l-4 border-l-pink-500">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <div className="p-2 rounded-lg bg-pink-100 text-pink-600">
                      <Palette className="h-5 w-5" />
                    </div>
                    Visual Style
                  </CardTitle>
                  <p className="text-sm text-gray-600 mt-1">
                    Choose the visual aesthetic for your AI-generated images
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {imageStyles.map((style) => (
                      <div
                        key={style.value}
                        className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-lg ${
                          imageStyle === style.value
                            ? "border-pink-500 bg-pink-50 shadow-md"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                        onClick={() => setImageStyle(style.value)}
                      >
                        <div className="flex items-start gap-3">
                          <style.icon className="h-6 w-6 text-gray-600 mt-0.5" />
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 mb-1">{style.label}</h3>
                            <p className="text-sm text-gray-600">{style.description}</p>
                          </div>
                        </div>
                        {imageStyle === style.value && (
                          <div className="absolute top-2 right-2">
                            <CheckCircle2 className="h-5 w-5 text-pink-600" />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Generate Button Card */}
              <Card className="shadow-sm hover:shadow-md transition-all duration-200 bg-gradient-to-r from-purple-50 via-pink-50 to-orange-50 border-purple-200">
                <CardContent className="p-8">
                  <div className="text-center space-y-6">
                    <div className="space-y-2">
                      <h3 className="text-xl font-semibold text-gray-900">Ready to Create Magic?</h3>
                      <p className="text-gray-600">
                        AI will generate stunning visual ads with images for each selected platform
                      </p>
                    </div>

                    <div className="flex justify-center">
                      <Button
                        onClick={generateAds}
                        disabled={!prompt.trim() || selectedPlatforms.length === 0 || isGenerating}
                        size="lg"
                        className="min-w-[320px] h-16 text-lg bg-gradient-to-r from-purple-600 via-pink-600 to-orange-600 hover:from-purple-700 hover:via-pink-700 hover:to-orange-700 text-white shadow-xl hover:shadow-2xl transition-all duration-200 transform hover:scale-105"
                      >
                        {isGenerating ? (
                          <>
                            <Loader2 className="h-6 w-6 mr-3 animate-spin" />
                            Creating Your Magical Ads...
                          </>
                        ) : (
                          <>
                            <Rocket className="h-6 w-6 mr-3" />
                            Generate Stunning Visual Ads
                            <ArrowRight className="h-5 w-5 ml-2" />
                          </>
                        )}
                      </Button>
                    </div>

                    {(!prompt.trim() || selectedPlatforms.length === 0) && (
                      <div className="text-sm text-gray-500 space-y-1">
                        {!prompt.trim() && <p>• Please enter a product description</p>}
                        {selectedPlatforms.length === 0 && <p>• Please select at least one platform</p>}
                      </div>
                    )}

                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                      <Sparkles className="h-4 w-4" />
                      <span>Each generation creates 2 unique ads per platform</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="results" className="space-y-8">
              {generatedAds.length > 0 ? (
                <>
                  {/* Results Header */}
                  <div className="text-center space-y-4">
                    <div className="flex items-center justify-center gap-2">
                      <CheckCircle2 className="h-6 w-6 text-green-600" />
                      <h2 className="text-2xl font-bold text-gray-900">
                        Your Stunning Ads Are Ready!
                      </h2>
                    </div>
                    <p className="text-gray-600 max-w-2xl mx-auto">
                      AI has created {generatedAds.length} unique visual ads across your selected platforms.
                      Each ad is optimized for its specific platform and audience.
                    </p>
                  </div>

                  {/* Generated Ads Grid */}
                  <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {generatedAds.map((ad, index) => (
                      <Card key={ad.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 group border-l-4 border-l-indigo-500">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="capitalize bg-blue-50 text-blue-700 border-blue-200">
                                {ad.platform}
                              </Badge>
                              <Badge variant="secondary" className="text-xs">
                                #{index + 1}
                              </Badge>
                            </div>
                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyAdContent(ad)}
                                className="hover:bg-blue-50"
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="hover:bg-green-50"
                              >
                                <Share2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          {/* Generated Image */}
                          {ad.media_assets && ad.media_assets.length > 0 && (
                            <div className="aspect-square rounded-xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 shadow-inner">
                              <img
                                src={ad.media_assets[0].url}
                                alt={ad.headline || "Generated ad image"}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                              />
                            </div>
                          )}

                          {/* Ad Content */}
                          <div className="space-y-3">
                            <h3 className="font-bold text-lg text-gray-900 leading-tight">
                              {ad.headline}
                            </h3>
                            <p className="text-sm text-gray-600 leading-relaxed">
                              {ad.description}
                            </p>
                            <div className="flex items-center justify-between">
                              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                                {ad.cta}
                              </Badge>
                              <Button variant="outline" size="sm" className="text-xs">
                                <Download className="h-3 w-3 mr-1" />
                                Export
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-center gap-4">
                    <Button
                      variant="outline"
                      onClick={() => setActiveTab("generate")}
                      className="flex items-center gap-2"
                    >
                      <RefreshCw className="h-4 w-4" />
                      Generate More Ads
                    </Button>
                    <Button
                      variant="default"
                      className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                    >
                      <Download className="h-4 w-4" />
                      Download All Ads
                    </Button>
                  </div>
                </>
              ) : (
                <div className="text-center py-20">
                  <div className="max-w-md mx-auto space-y-6">
                    <div className="p-6 rounded-full bg-gray-100 w-fit mx-auto">
                      <ImageIcon className="h-12 w-12 text-gray-400" />
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-xl font-semibold text-gray-900">No Ads Generated Yet</h3>
                      <p className="text-gray-600">
                        Head to the Generate tab to create your first stunning visual ad campaign!
                      </p>
                    </div>
                    <Button
                      onClick={() => setActiveTab("generate")}
                      className="flex items-center gap-2"
                    >
                      <Rocket className="h-4 w-4" />
                      Start Creating
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="history" className="space-y-8">
              {history.length > 0 ? (
                <>
                  <div className="text-center space-y-2">
                    <h2 className="text-2xl font-bold text-gray-900">Your Creative History</h2>
                    <p className="text-gray-600">
                      Revisit your past ad campaigns and reload successful prompts
                    </p>
                  </div>

                  <div className="space-y-6">
                    {history.map((entry, index) => (
                      <Card key={entry.id} className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-green-500">
                        <CardContent className="p-6">
                          <div className="flex items-start justify-between gap-4">
                            <div className="flex-1 space-y-3">
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                  #{history.length - index}
                                </Badge>
                                <span className="text-sm text-gray-500 flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {new Date(entry.timestamp).toLocaleDateString('en-US', {
                                    month: 'short',
                                    day: 'numeric',
                                    year: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })}
                                </span>
                              </div>

                              <p className="font-medium text-gray-900 leading-relaxed line-clamp-3">
                                {entry.prompt}
                              </p>

                              <div className="flex items-center gap-3 flex-wrap">
                                <div className="flex items-center gap-1">
                                  <Badge variant="secondary" className="text-xs">
                                    <Sparkles className="h-3 w-3 mr-1" />
                                    {entry.adsCount} ads
                                  </Badge>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Badge variant="secondary" className="text-xs">
                                    <Target className="h-3 w-3 mr-1" />
                                    {entry.platforms.length} platforms
                                  </Badge>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Badge variant="secondary" className="text-xs">
                                    <Palette className="h-3 w-3 mr-1" />
                                    {entry.imageStyle}
                                  </Badge>
                                </div>
                              </div>

                              <div className="flex items-center gap-2">
                                <span className="text-xs text-gray-500">Platforms:</span>
                                {entry.platforms.map((platform) => {
                                  const platformData = platforms.find(p => p.value === platform);
                                  const IconComponent = platformData?.icon;
                                  return IconComponent ? (
                                    <IconComponent key={platform} className="h-4 w-4 text-gray-600" title={platformData?.label} />
                                  ) : null;
                                })}
                              </div>
                            </div>

                            <div className="flex flex-col gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => loadFromHistory(entry)}
                                className="flex items-center gap-2 hover:bg-blue-50"
                              >
                                <RefreshCw className="h-4 w-4" />
                                Reload
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="flex items-center gap-2 hover:bg-gray-50"
                              >
                                <Eye className="h-4 w-4" />
                                View
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </>
              ) : (
                <div className="text-center py-20">
                  <div className="max-w-md mx-auto space-y-6">
                    <div className="p-6 rounded-full bg-gray-100 w-fit mx-auto">
                      <History className="h-12 w-12 text-gray-400" />
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-xl font-semibold text-gray-900">No History Yet</h3>
                      <p className="text-gray-600">
                        Your generated ad campaigns will appear here for easy access and reuse
                      </p>
                    </div>
                    <Button
                      onClick={() => setActiveTab("generate")}
                      className="flex items-center gap-2"
                    >
                      <Rocket className="h-4 w-4" />
                      Create Your First Campaign
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Footer */}
      <DashboardFooter />
    </div>
  );
}
