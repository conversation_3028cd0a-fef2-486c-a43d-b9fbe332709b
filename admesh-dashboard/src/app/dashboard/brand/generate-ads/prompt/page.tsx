"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

import { toast } from "sonner";
import {
  Sparkles,
  Image as ImageIcon,
  Loader2,
  History,
  Download,
  Copy,
  Wand2,
  Trash2,
  Cloud,
  HardDrive
} from "lucide-react";
import { saveAdsToStorage, getSavedAds, deleteAdBatch, getStorageStats, type StoredAd } from "@/lib/storage";

interface GeneratedAd {
  id: string;
  type: string;
  content: string;
  headline?: string;
  description?: string;
  cta?: string;
  platform: string;
  format: string;
  media_assets?: Array<{
    id: string;
    type: string;
    url: string;
    storage_path: string;
    metadata: Record<string, any>;
  }>;
  generation_metadata?: Record<string, any>;
}

interface HistoryEntry {
  id: string;
  prompt: string;
  platforms: string[];
  imageStyle: string;
  adsCount: number;
  timestamp: string;
  ads: GeneratedAd[];
}

export default function PromptAdGeneratorPage() {
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedAds, setGeneratedAds] = useState<GeneratedAd[]>([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(["facebook", "instagram"]);
  const [imageStyle, setImageStyle] = useState("creative");
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [activeTab, setActiveTab] = useState("generate");
  const [savedAds, setSavedAds] = useState<StoredAd[]>([]);
  const [savedBatches, setSavedBatches] = useState<any[]>([]);
  const [isLoadingSaved, setIsLoadingSaved] = useState(false);
  const [storageStats, setStorageStats] = useState({ totalAds: 0, totalBatches: 0, storageUsed: 0 });

  const platforms = [
    { value: "facebook", label: "Facebook" },
    { value: "instagram", label: "Instagram" },
    { value: "google", label: "Google Ads" },
    { value: "linkedin", label: "LinkedIn" },
    { value: "twitter", label: "Twitter/X" },
    { value: "tiktok", label: "TikTok" },
    { value: "youtube", label: "YouTube" }
  ];

  const imageStyles = [
    { value: "creative", label: "Creative" },
    { value: "bold", label: "Bold" },
    { value: "minimalist", label: "Minimal" },
    { value: "professional", label: "Professional" }
  ];

  const promptExamples = [
    "Revolutionary AI-powered fitness app that reads your mind",
    "Eco-friendly sneakers made from ocean plastic",
    "Premium coffee that gives you superpowers",
    "Dating app for introverts",
    "Time-travel vacation packages"
  ];

  useEffect(() => {
    loadHistory();
  }, []);

  const generateAds = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt for your ad");
      return;
    }

    if (selectedPlatforms.length === 0) {
      toast.error("Please select at least one platform");
      return;
    }

    setIsGenerating(true);

    try {
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) {
        toast.error("Please log in to generate ads");
        return;
      }

      const token = await getIdToken(auth.currentUser);
      const allGeneratedAds: GeneratedAd[] = [];
      
      // Generate ads for all selected platforms
      for (const platform of selectedPlatforms) {
        try {
          const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';
          const response = await fetch(`${apiBaseUrl}/ad-generation/generate`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            },
            body: JSON.stringify({
              product_name: extractProductName(prompt),
              product_description: prompt,
              target_audience: "General audience",
              ad_type: "multimodal",
              platform: platform,
              tone: "creative",
              keywords: "",
              cta: "Learn More",
              additional_info: "Make it creative and eye-catching!",
              num_variations: 2,
              generate_images: true,
              generate_videos: false,
              image_style: imageStyle,
              video_duration: 15,
              brand_colors: [],
              brand_fonts: [],
              use_brand_assets: true
            })
          });

          if (response.ok) {
            const data = await response.json();
            if (data.success && data.ads) {
              allGeneratedAds.push(...data.ads);
            }
          }
        } catch (error) {
          console.error(`Error generating ads for ${platform}:`, error);
        }
      }

      if (allGeneratedAds.length > 0) {
        setGeneratedAds(allGeneratedAds);

        // Save ads to Firebase Storage
        try {
          const storageMetadata = {
            prompt: prompt,
            platforms: selectedPlatforms,
            imageStyle: imageStyle,
            timestamp: new Date().toISOString(),
            totalAds: allGeneratedAds.length
          };

          await saveAdsToStorage(allGeneratedAds, storageMetadata);
          toast.success(`Generated and saved ${allGeneratedAds.length} ads successfully!`);

          // Reload saved ads to update the list
          await loadSavedAds();
        } catch (storageError) {
          console.error("Error saving ads to storage:", storageError);
          toast.success(`Generated ${allGeneratedAds.length} ads successfully!`);
          toast.error("Failed to save ads to storage");
        }

        // Add to local history
        const historyEntry: HistoryEntry = {
          id: Date.now().toString(),
          prompt: prompt,
          platforms: selectedPlatforms,
          imageStyle: imageStyle,
          adsCount: allGeneratedAds.length,
          timestamp: new Date().toISOString(),
          ads: allGeneratedAds
        };
        setHistory(prev => [historyEntry, ...prev]);
        setActiveTab("results");
      } else {
        throw new Error("Failed to generate any ads");
      }
    } catch (error) {
      console.error("Error generating ads:", error);
      toast.error(error instanceof Error ? error.message : "Failed to generate ads");
    } finally {
      setIsGenerating(false);
    }
  };

  const extractProductName = (prompt: string): string => {
    const words = prompt.split(' ');
    if (words.length <= 3) return prompt;
    
    const productIndicators = ['for', 'about', 'selling', 'promoting'];
    for (const indicator of productIndicators) {
      const index = words.findIndex(word => word.toLowerCase().includes(indicator));
      if (index > 0) {
        return words.slice(0, index).join(' ');
      }
    }
    
    return words.slice(0, 3).join(' ');
  };

  const loadHistory = async () => {
    try {
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) return;

      const token = await getIdToken(auth.currentUser);
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';
      const response = await fetch(`${apiBaseUrl}/ad-generation/history?limit=20`, {
        headers: {
          "Authorization": `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.history) {
          setHistory(data.history);
        }
      }
    } catch (error) {
      console.error("Error loading history:", error);
    }
  };

  const handlePlatformToggle = (platform: string) => {
    setSelectedPlatforms(prev =>
      prev.includes(platform)
        ? prev.filter(p => p !== platform)
        : [...prev, platform]
    );
  };

  // Load saved ads from Firebase Storage
  const loadSavedAds = async () => {
    setIsLoadingSaved(true);
    try {
      const { ads, batches } = await getSavedAds();
      setSavedAds(ads);
      setSavedBatches(batches);

      // Update storage stats
      const stats = await getStorageStats();
      setStorageStats(stats);
    } catch (error) {
      console.error("Error loading saved ads:", error);
      toast.error("Failed to load saved ads");
    } finally {
      setIsLoadingSaved(false);
    }
  };

  // Delete a batch of ads
  const handleDeleteBatch = async (batchId: string) => {
    try {
      await deleteAdBatch(batchId);
      toast.success("Batch deleted successfully");
      // Reload saved ads
      await loadSavedAds();
    } catch (error) {
      console.error("Error deleting batch:", error);
      toast.error("Failed to delete batch");
    }
  };

  // Load saved ads on component mount
  useEffect(() => {
    loadSavedAds();
  }, []);

  const copyAdContent = (ad: GeneratedAd | StoredAd) => {
    const content = `${ad.headline}\n\n${ad.description}\n\n${ad.cta}`;
    navigator.clipboard.writeText(content);
    toast.success("Ad content copied to clipboard!");
  };

  const loadFromHistory = (entry: HistoryEntry) => {
    setPrompt(entry.prompt);
    setSelectedPlatforms(entry.platforms);
    setImageStyle(entry.imageStyle);
    setGeneratedAds(entry.ads);
    setActiveTab("results");
    toast.success("Loaded from history!");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Simple Header */}
      <div className="bg-white border-b">
        <div className="px-6 py-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Wand2 className="h-5 w-5 text-gray-600" />
                <h1 className="text-xl font-semibold text-gray-900">
                  AI Ad Generator
                </h1>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setActiveTab(activeTab === "history" ? "generate" : "history")}
              >
                <History className="h-4 w-4 mr-1" />
                History
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-6 py-6">
        <div className="max-w-4xl mx-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="generate">Generate</TabsTrigger>
              <TabsTrigger value="results">Results ({generatedAds.length})</TabsTrigger>
              <TabsTrigger value="saved">
                Saved ({savedBatches.length})
              </TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
            </TabsList>

            <TabsContent value="generate" className="space-y-6">
              {/* Prompt Input */}
              <Card>
                <CardHeader>
                  <CardTitle>What do you want to advertise?</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    placeholder="Describe your product or service..."
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    className="min-h-[100px]"
                  />

                  {/* Example prompts */}
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">Examples:</p>
                    <div className="flex flex-wrap gap-2">
                      {promptExamples.map((example, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => setPrompt(example)}
                          className="text-xs h-8"
                        >
                          {example.slice(0, 30)}...
                        </Button>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Platform Selection */}
              <Card>
                <CardHeader>
                  <CardTitle>Select Platforms</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {platforms.map((platform) => (
                      <div
                        key={platform.value}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedPlatforms.includes(platform.value)
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                        onClick={() => {
                          if (selectedPlatforms.includes(platform.value)) {
                            setSelectedPlatforms(selectedPlatforms.filter(p => p !== platform.value));
                          } else {
                            setSelectedPlatforms([...selectedPlatforms, platform.value]);
                          }
                        }}
                      >
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={selectedPlatforms.includes(platform.value)}
                            readOnly
                          />
                          <span className="text-sm font-medium">{platform.label}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Image Style */}
              <Card>
                <CardHeader>
                  <CardTitle>Image Style</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {imageStyles.map((style) => (
                      <Button
                        key={style.value}
                        variant={imageStyle === style.value ? "default" : "outline"}
                        onClick={() => setImageStyle(style.value)}
                        className="h-auto p-3"
                      >
                        {style.label}
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Generate Button */}
              <div className="flex justify-center">
                <Button
                  onClick={generateAds}
                  disabled={!prompt.trim() || selectedPlatforms.length === 0 || isGenerating}
                  size="lg"
                  className="min-w-[200px]"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-4 w-4 mr-2" />
                      Generate Ads
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="results" className="space-y-6">
              {generatedAds.length > 0 ? (
                <div className="grid gap-6 md:grid-cols-2">
                  {generatedAds.map((ad) => (
                    <Card key={ad.id}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="capitalize">
                            {ad.platform}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyAdContent(ad)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {/* Generated Image */}
                        {ad.media_assets && ad.media_assets.length > 0 && (
                          <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                            <img
                              src={ad.media_assets[0].url}
                              alt={ad.headline || "Generated ad image"}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        )}

                        {/* Ad Content */}
                        <div className="space-y-2">
                          <h3 className="font-semibold text-lg">{ad.headline}</h3>
                          <p className="text-sm text-gray-600">{ad.description}</p>
                          <Badge variant="secondary">{ad.cta}</Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No ads generated yet</h3>
                  <p className="text-gray-600">
                    Go to the Generate tab to create your first ad!
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="saved" className="space-y-6">
              {/* Storage Stats */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Cloud className="h-5 w-5" />
                    Storage Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-blue-600">{storageStats.totalBatches}</div>
                      <div className="text-sm text-gray-600">Batches</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">{storageStats.totalAds}</div>
                      <div className="text-sm text-gray-600">Total Ads</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-purple-600">
                        {(storageStats.storageUsed / 1024 / 1024).toFixed(2)} MB
                      </div>
                      <div className="text-sm text-gray-600">Storage Used</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Saved Ad Batches */}
              {isLoadingSaved ? (
                <div className="text-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p>Loading saved ads...</p>
                </div>
              ) : savedBatches.length > 0 ? (
                <div className="space-y-4">
                  {savedBatches.map((batch) => (
                    <Card key={batch.batchId}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-lg">{batch.prompt.slice(0, 60)}...</CardTitle>
                            <p className="text-sm text-gray-600 mt-1">
                              {new Date(batch.savedAt).toLocaleDateString()} • {batch.totalAds} ads • {batch.platforms.join(", ")}
                            </p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => loadSavedAds()}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Refresh
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteBatch(batch.batchId)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                          {batch.ads.slice(0, 4).map((ad: StoredAd) => (
                            <div key={ad.id} className="p-3 border rounded-lg">
                              <div className="flex items-center justify-between mb-2">
                                <Badge variant="outline" className="capitalize">
                                  {ad.platform}
                                </Badge>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyAdContent(ad)}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                              {ad.media_assets && ad.media_assets.length > 0 && (
                                <div className="aspect-video rounded bg-gray-100 mb-2 overflow-hidden">
                                  <img
                                    src={ad.media_assets[0].url}
                                    alt={ad.headline}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                              )}
                              <h4 className="font-medium text-sm mb-1">{ad.headline}</h4>
                              <p className="text-xs text-gray-600 line-clamp-2">{ad.description}</p>
                            </div>
                          ))}
                        </div>
                        {batch.ads.length > 4 && (
                          <p className="text-sm text-gray-500 mt-3 text-center">
                            +{batch.ads.length - 4} more ads in this batch
                          </p>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <HardDrive className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No saved ads yet</h3>
                  <p className="text-gray-600">
                    Generate some ads and they'll be automatically saved here
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="history" className="space-y-6">
              {history.length > 0 ? (
                <div className="space-y-4">
                  {history.map((entry) => (
                    <Card key={entry.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <p className="font-medium line-clamp-2">{entry.prompt}</p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="outline">{entry.adsCount} ads</Badge>
                              <Badge variant="outline">{entry.platforms.length} platforms</Badge>
                              <Badge variant="outline">{entry.imageStyle}</Badge>
                              <span className="text-xs text-gray-500">
                                {new Date(entry.timestamp).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => loadFromHistory(entry)}
                          >
                            Load
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No history yet</h3>
                  <p className="text-gray-600">
                    Your generated ads will appear here
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
