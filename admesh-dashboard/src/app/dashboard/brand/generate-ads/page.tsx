"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Wand2,
  Sparkles,
  Download,
  DownloadCloud,
  Copy,
  RefreshCw,
  Image as ImageIcon,
  Video,
  FileText,
  Loader2,
  CheckCircle,
  AlertCircle,
  Share2,
  Eye,
  Palette,
  Target,
  MessageSquare,
  Settings,
  Z<PERSON>,
  TrendingUp
} from "lucide-react";
import { toast } from "sonner";
import DashboardFooter from "@/components/DashboardFooter";

interface GeneratedAd {
  id: string;
  type: 'text' | 'image' | 'video' | 'multimodal';
  content: string;
  headline?: string;
  description?: string;
  cta?: string;
  imageUrl?: string;
  videoUrl?: string;
  platform?: string;
  format?: string;
}

interface FormData {
  productName: string;
  productDescription: string;
  targetAudience: string;
  adType: string;
  platform: string;
  tone: string;
  keywords: string;
  cta: string;
  additionalInfo: string;
  generateImages: boolean;
  generateVideos: boolean;
  imageStyle: string;
  videoDuration: string;
  brandColors: string;
  brandFonts: string;
  useBrandAssets: boolean;
}

export default function GenerateAdsPage() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedAds, setGeneratedAds] = useState<GeneratedAd[]>([]);
  const [previewMode, setPreviewMode] = useState<'content' | 'preview'>('content');
  const [formData, setFormData] = useState<FormData>({
    productName: "",
    productDescription: "",
    targetAudience: "",
    adType: "text",
    platform: "facebook",
    tone: "professional",
    keywords: "",
    cta: "",
    additionalInfo: "",
    generateImages: false,
    generateVideos: false,
    imageStyle: "professional",
    videoDuration: "30",
    brandColors: "",
    brandFonts: "",
    useBrandAssets: false
  });

  const adTypes = [
    { value: "text", label: "Text Ad", icon: FileText, description: "Pure text-based advertisements" },
    { value: "image", label: "Image Ad", icon: ImageIcon, description: "Visual ads with compelling imagery" },
    { value: "video", label: "Video Ad", icon: Video, description: "Engaging video content" },
    { value: "multimodal", label: "Multimodal Ad", icon: Sparkles, description: "Combined text, image, and video" }
  ];

  const imageStyles = [
    { value: "professional", label: "Professional", description: "Clean, corporate aesthetic" },
    { value: "creative", label: "Creative", description: "Artistic and innovative designs" },
    { value: "minimalist", label: "Minimalist", description: "Simple, clean layouts" },
    { value: "bold", label: "Bold", description: "High-contrast, attention-grabbing" },
    { value: "modern", label: "Modern", description: "Contemporary design trends" },
    { value: "vintage", label: "Vintage", description: "Retro and classic styling" }
  ];

  const platforms = [
    { value: "facebook", label: "Facebook", icon: "📘", description: "Social media advertising" },
    { value: "instagram", label: "Instagram", icon: "📷", description: "Visual storytelling platform" },
    { value: "google", label: "Google Ads", icon: "🔍", description: "Search and display advertising" },
    { value: "linkedin", label: "LinkedIn", icon: "💼", description: "Professional networking" },
    { value: "twitter", label: "Twitter/X", icon: "🐦", description: "Real-time social updates" },
    { value: "tiktok", label: "TikTok", icon: "🎵", description: "Short-form video content" },
    { value: "youtube", label: "YouTube", icon: "📺", description: "Video advertising platform" }
  ];

  const tones = [
    { value: "professional", label: "Professional", description: "Formal and authoritative" },
    { value: "casual", label: "Casual", description: "Relaxed and approachable" },
    { value: "friendly", label: "Friendly", description: "Warm and welcoming" },
    { value: "urgent", label: "Urgent", description: "Time-sensitive and compelling" },
    { value: "humorous", label: "Humorous", description: "Light-hearted and entertaining" },
    { value: "inspirational", label: "Inspirational", description: "Motivating and uplifting" },
    { value: "luxury", label: "Luxury", description: "Premium and exclusive" },
    { value: "conversational", label: "Conversational", description: "Natural and engaging" }
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const generateAds = async () => {
    if (!formData.productName || !formData.productDescription) {
      toast.error("Please fill in the product name and description");
      return;
    }

    setIsGenerating(true);

    try {
      // Get Firebase auth token
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) {
        toast.error("Please log in to generate ads");
        return;
      }

      const token = await getIdToken(auth.currentUser);

      // Call the ad generation API
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/ad-generation/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        },
        body: JSON.stringify({
          product_name: formData.productName,
          product_description: formData.productDescription,
          target_audience: formData.targetAudience,
          ad_type: formData.adType,
          platform: formData.platform,
          tone: formData.tone,
          keywords: formData.keywords,
          cta: formData.cta,
          additional_info: formData.additionalInfo,
          num_variations: 3,
          // New multimodal parameters
          generate_images: formData.generateImages,
          generate_videos: formData.generateVideos,
          image_style: formData.imageStyle,
          video_duration: formData.videoDuration,
          brand_colors: formData.brandColors,
          brand_fonts: formData.brandFonts,
          use_brand_assets: formData.useBrandAssets
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to generate ads");
      }

      const data = await response.json();

      if (data.success && data.ads) {
        setGeneratedAds(data.ads);
        toast.success(data.message || "Ads generated successfully!");
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (error) {
      console.error("Ad generation error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to generate ads. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success("Copied to clipboard!");
  };

  const downloadAd = (ad: GeneratedAd, format: 'txt' | 'json' | 'csv' = 'txt') => {
    let content: string;
    let filename: string;
    let mimeType: string;

    const baseFilename = `${ad.headline?.replace(/\s+/g, '_') || 'ad'}_${ad.id}`;

    switch (format) {
      case 'json':
        content = JSON.stringify(ad, null, 2);
        filename = `${baseFilename}.json`;
        mimeType = 'application/json';
        break;
      case 'csv':
        content = `Headline,Description,CTA,Content,Platform,Type\n"${ad.headline}","${ad.description}","${ad.cta}","${ad.content?.replace(/"/g, '""')}","${ad.platform}","${ad.type}"`;
        filename = `${baseFilename}.csv`;
        mimeType = 'text/csv';
        break;
      default:
        content = `HEADLINE: ${ad.headline}\n\nDESCRIPTION: ${ad.description}\n\nCTA: ${ad.cta}\n\nFULL CONTENT:\n${ad.content}\n\nPLATFORM: ${ad.platform}\nTYPE: ${ad.type}`;
        filename = `${baseFilename}.txt`;
        mimeType = 'text/plain';
    }

    const element = document.createElement("a");
    const file = new Blob([content], { type: mimeType });
    element.href = URL.createObjectURL(file);
    element.download = filename;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    toast.success(`Ad downloaded as ${format.toUpperCase()}!`);
  };

  const downloadAllAds = (format: 'txt' | 'json' | 'csv' = 'json') => {
    if (generatedAds.length === 0) {
      toast.error("No ads to download");
      return;
    }

    let content: string;
    let filename: string;
    let mimeType: string;

    switch (format) {
      case 'json':
        content = JSON.stringify(generatedAds, null, 2);
        filename = `generated_ads_${Date.now()}.json`;
        mimeType = 'application/json';
        break;
      case 'csv':
        const headers = 'Headline,Description,CTA,Content,Platform,Type\n';
        const rows = generatedAds.map(ad =>
          `"${ad.headline}","${ad.description}","${ad.cta}","${ad.content?.replace(/"/g, '""')}","${ad.platform}","${ad.type}"`
        ).join('\n');
        content = headers + rows;
        filename = `generated_ads_${Date.now()}.csv`;
        mimeType = 'text/csv';
        break;
      default:
        content = generatedAds.map((ad, index) =>
          `=== AD ${index + 1} ===\nHEADLINE: ${ad.headline}\nDESCRIPTION: ${ad.description}\nCTA: ${ad.cta}\nFULL CONTENT:\n${ad.content}\nPLATFORM: ${ad.platform}\nTYPE: ${ad.type}\n\n`
        ).join('');
        filename = `generated_ads_${Date.now()}.txt`;
        mimeType = 'text/plain';
    }

    const element = document.createElement("a");
    const file = new Blob([content], { type: mimeType });
    element.href = URL.createObjectURL(file);
    element.download = filename;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    toast.success(`All ads downloaded as ${format.toUpperCase()}!`);
  };

  const shareAd = async (ad: GeneratedAd) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: ad.headline,
          text: ad.content,
          url: window.location.href
        });
        toast.success("Ad shared successfully!");
      } catch (error) {
        if ((error as Error).name !== 'AbortError') {
          copyToClipboard(ad.content);
        }
      }
    } else {
      copyToClipboard(ad.content);
    }
  };

  const AdPreview = ({ ad }: { ad: GeneratedAd }) => {
    const platformStyles = {
      facebook: {
        container: "bg-white border border-gray-200 rounded-lg p-4 max-w-md",
        header: "flex items-center gap-3 mb-3",
        avatar: "w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold",
        name: "font-semibold text-gray-900",
        time: "text-gray-500 text-sm",
        content: "text-gray-800 mb-3",
        cta: "bg-blue-600 text-white px-4 py-2 rounded-md font-medium text-sm"
      },
      instagram: {
        container: "bg-white border border-gray-200 rounded-lg overflow-hidden max-w-md",
        header: "flex items-center gap-3 p-4",
        avatar: "w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm",
        name: "font-semibold text-gray-900 text-sm",
        content: "px-4 pb-4 text-gray-800 text-sm",
        cta: "text-blue-600 font-medium"
      },
      google: {
        container: "bg-white border border-gray-200 rounded p-4 max-w-md",
        headline: "text-blue-600 text-lg font-medium hover:underline cursor-pointer",
        url: "text-green-700 text-sm",
        description: "text-gray-700 text-sm mt-1"
      },
      linkedin: {
        container: "bg-white border border-gray-200 rounded-lg p-4 max-w-md",
        header: "flex items-center gap-3 mb-3",
        avatar: "w-10 h-10 bg-blue-700 rounded-full flex items-center justify-center text-white font-bold",
        name: "font-semibold text-gray-900",
        title: "text-gray-600 text-sm",
        content: "text-gray-800 mb-3",
        cta: "bg-blue-700 text-white px-4 py-2 rounded-md font-medium text-sm"
      }
    };

    const style = platformStyles[ad.platform as keyof typeof platformStyles] || platformStyles.facebook;

    if (ad.platform === 'google') {
      return (
        <div className={style.container}>
          <div className={style.headline}>{ad.headline}</div>
          <div className={style.url}>www.example.com</div>
          <div className={style.description}>{ad.description}</div>
        </div>
      );
    }

    return (
      <div className={style.container}>
        <div className={style.header}>
          <div className={style.avatar}>B</div>
          <div>
            <div className={style.name}>Your Brand</div>
            {ad.platform === 'linkedin' && <div className={style.title}>Company</div>}
            {ad.platform !== 'linkedin' && <div className="text-gray-500 text-sm">Sponsored</div>}
          </div>
        </div>
        <div className={style.content}>
          {ad.content}
        </div>
        <button className={style.cta}>
          {ad.cta}
        </button>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      {/* Enhanced Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/60 sticky top-0 z-10">
        <div className="dashboard-header">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col sm:flex-row sm:items-center gap-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg">
                  <Wand2 className="h-7 w-7" />
                </div>
                <div>
                  <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 tracking-tight">
                    AI Ad Generator
                  </h1>
                  <p className="mt-2 text-base text-gray-600 max-w-2xl">
                    Create high-converting ads with AI-powered content generation. Transform your product descriptions into compelling marketing campaigns.
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3 sm:ml-auto">
                <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
                  <Zap className="h-3 w-3 mr-1" />
                  AI Powered
                </Badge>
                <Badge variant="outline" className="border-blue-200 text-blue-700">
                  BETA
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="dashboard-content">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Input Form - Takes 2 columns on xl screens */}
            <div className="xl:col-span-2 space-y-8">
              {/* Product Information Card */}
              <Card className="shadow-sm hover:shadow-md transition-all duration-200">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                      <Target className="h-5 w-5" />
                    </div>
                    Product Information
                  </CardTitle>
                  <p className="text-sm text-gray-600 mt-1">Tell us about your product to generate targeted ads</p>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="md:col-span-2">
                      <Label htmlFor="productName" className="text-sm font-medium text-gray-700">
                        Product Name *
                      </Label>
                      <Input
                        id="productName"
                        placeholder="e.g., EcoSmart Water Bottle"
                        value={formData.productName}
                        onChange={(e) => handleInputChange("productName", e.target.value)}
                        className="mt-1.5"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <Label htmlFor="productDescription" className="text-sm font-medium text-gray-700">
                        Product Description *
                      </Label>
                      <Textarea
                        id="productDescription"
                        placeholder="Describe your product's key features, benefits, and what makes it unique..."
                        value={formData.productDescription}
                        onChange={(e) => handleInputChange("productDescription", e.target.value)}
                        rows={4}
                        className="mt-1.5"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <Label htmlFor="targetAudience" className="text-sm font-medium text-gray-700">
                        Target Audience
                      </Label>
                      <Input
                        id="targetAudience"
                        placeholder="e.g., Environmentally conscious millennials, Fitness enthusiasts"
                        value={formData.targetAudience}
                        onChange={(e) => handleInputChange("targetAudience", e.target.value)}
                        className="mt-1.5"
                      />
                      <p className="text-xs text-gray-500 mt-1">Who is your ideal customer?</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Ad Configuration Card */}
              <Card className="shadow-sm hover:shadow-md transition-all duration-200">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <div className="p-2 rounded-lg bg-purple-100 text-purple-600">
                      <Settings className="h-5 w-5" />
                    </div>
                    Ad Configuration
                  </CardTitle>
                  <p className="text-sm text-gray-600 mt-1">Customize your ad format and targeting</p>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Ad Type</Label>
                      <Select value={formData.adType} onValueChange={(value) => handleInputChange("adType", value)}>
                        <SelectTrigger className="mt-1.5">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {adTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              <div className="flex items-center gap-3 py-1">
                                <type.icon className="h-4 w-4" />
                                <div>
                                  <div className="font-medium">{type.label}</div>
                                  <div className="text-xs text-gray-500">{type.description}</div>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-gray-700">Platform</Label>
                      <Select value={formData.platform} onValueChange={(value) => handleInputChange("platform", value)}>
                        <SelectTrigger className="mt-1.5">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {platforms.map((platform) => (
                            <SelectItem key={platform.value} value={platform.value}>
                              <div className="flex items-center gap-3 py-1">
                                <span className="text-lg">{platform.icon}</span>
                                <div>
                                  <div className="font-medium">{platform.label}</div>
                                  <div className="text-xs text-gray-500">{platform.description}</div>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-gray-700">Tone & Style</Label>
                      <Select value={formData.tone} onValueChange={(value) => handleInputChange("tone", value)}>
                        <SelectTrigger className="mt-1.5">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {tones.map((tone) => (
                            <SelectItem key={tone.value} value={tone.value}>
                              <div className="py-1">
                                <div className="font-medium">{tone.label}</div>
                                <div className="text-xs text-gray-500">{tone.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="cta" className="text-sm font-medium text-gray-700">Call to Action</Label>
                      <Input
                        id="cta"
                        placeholder="e.g., Shop Now, Learn More, Get Started"
                        value={formData.cta}
                        onChange={(e) => handleInputChange("cta", e.target.value)}
                        className="mt-1.5"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <Label htmlFor="keywords" className="text-sm font-medium text-gray-700">Keywords</Label>
                      <Input
                        id="keywords"
                        placeholder="sustainable, eco-friendly, premium, innovative (comma separated)"
                        value={formData.keywords}
                        onChange={(e) => handleInputChange("keywords", e.target.value)}
                        className="mt-1.5"
                      />
                      <p className="text-xs text-gray-500 mt-1">Keywords help focus the ad content</p>
                    </div>

                    <div className="md:col-span-2">
                      <Label htmlFor="additionalInfo" className="text-sm font-medium text-gray-700">Additional Requirements</Label>
                      <Textarea
                        id="additionalInfo"
                        placeholder="Any specific requirements, brand guidelines, or creative direction..."
                        value={formData.additionalInfo}
                        onChange={(e) => handleInputChange("additionalInfo", e.target.value)}
                        rows={3}
                        className="mt-1.5"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Generate Button Card */}
              <Card className="shadow-sm hover:shadow-md transition-all duration-200 bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200">
                <CardContent className="p-6">
                  <div className="text-center space-y-4">
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-600 mb-4">
                      <TrendingUp className="h-4 w-4" />
                      <span>Ready to create high-converting ads?</span>
                    </div>
                    <Button
                      onClick={generateAds}
                      disabled={isGenerating || !formData.productName || !formData.productDescription}
                      className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-5 w-5 animate-spin mr-2" />
                          Generating Your Ads...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-5 w-5 mr-2" />
                          Generate AI-Powered Ads
                        </>
                      )}
                    </Button>
                    <p className="text-xs text-gray-500">
                      AI will create multiple ad variations optimized for your selected platform
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Generated Ads Sidebar */}
            <div className="xl:col-span-1 space-y-6">
              {/* Results Header */}
              <Card className="shadow-sm">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-3 text-xl">
                      <div className="p-2 rounded-lg bg-green-100 text-green-600">
                        <Sparkles className="h-5 w-5" />
                      </div>
                      Generated Ads
                    </CardTitle>
                    {generatedAds.length > 0 && (
                      <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        {generatedAds.length} ads
                      </Badge>
                    )}
                  </div>
                  {generatedAds.length > 0 && (
                    <p className="text-sm text-gray-600 mt-1">Your AI-generated ad variations are ready</p>
                  )}
                </CardHeader>

                {generatedAds.length > 0 && (
                  <CardContent className="pt-0">
                    <div className="flex flex-col gap-3">
                      {/* View Mode Toggle */}
                      <div className="flex items-center gap-2 p-1 bg-gray-100 rounded-lg">
                        <Button
                          variant={previewMode === 'content' ? 'default' : 'ghost'}
                          size="sm"
                          onClick={() => setPreviewMode('content')}
                          className="flex-1"
                        >
                          <FileText className="h-4 w-4 mr-1" />
                          Content
                        </Button>
                        <Button
                          variant={previewMode === 'preview' ? 'default' : 'ghost'}
                          size="sm"
                          onClick={() => setPreviewMode('preview')}
                          className="flex-1"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Preview
                        </Button>
                      </div>

                      {/* Export Options */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" className="w-full">
                            <DownloadCloud className="h-4 w-4 mr-2" />
                            Export All Ads
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="w-full">
                          <DropdownMenuItem onClick={() => downloadAllAds('txt')}>
                            <FileText className="h-4 w-4 mr-2" />
                            Download as TXT
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => downloadAllAds('json')}>
                            <FileText className="h-4 w-4 mr-2" />
                            Download as JSON
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => downloadAllAds('csv')}>
                            <FileText className="h-4 w-4 mr-2" />
                            Download as CSV
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                )}
              </Card>

              {/* Empty State */}
              {generatedAds.length === 0 && !isGenerating && (
                <Card className="border-dashed border-2 border-gray-300 bg-gray-50/50">
                  <CardContent className="flex flex-col items-center justify-center py-16 text-center">
                    <div className="p-4 rounded-full bg-gray-100 mb-6">
                      <MessageSquare className="h-8 w-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Ready to Generate Ads</h3>
                    <p className="text-gray-600 mb-6 max-w-sm">
                      Fill in your product information and click "Generate AI-Powered Ads" to create compelling marketing content
                    </p>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <Sparkles className="h-4 w-4" />
                      <span>AI will create multiple variations for you</span>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Loading State */}
              {isGenerating && (
                <Card className="bg-gradient-to-br from-indigo-50 to-purple-50 border-indigo-200">
                  <CardContent className="flex flex-col items-center justify-center py-16 text-center">
                    <div className="relative mb-6">
                      <div className="p-4 rounded-full bg-white shadow-lg">
                        <Loader2 className="h-8 w-8 text-indigo-600 animate-spin" />
                      </div>
                      <div className="absolute -top-1 -right-1 p-1 rounded-full bg-purple-100">
                        <Sparkles className="h-4 w-4 text-purple-600" />
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">AI is Creating Your Ads</h3>
                    <p className="text-gray-600 mb-4">Analyzing your product and generating compelling content...</p>
                    <div className="flex items-center gap-2 text-sm text-indigo-600">
                      <div className="w-2 h-2 bg-indigo-600 rounded-full animate-pulse"></div>
                      <span>This usually takes 10-30 seconds</span>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Generated Ads List */}
              {generatedAds.map((ad, index) => (
                <Card key={ad.id} className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-indigo-500">
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="outline" className="text-xs">
                            Ad #{index + 1}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {ad.type}
                          </Badge>
                          <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                            {ad.platform}
                          </Badge>
                        </div>
                        <CardTitle className="text-lg text-gray-900 leading-tight">
                          {ad.headline || `${ad.type} Ad for ${ad.platform}`}
                        </CardTitle>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Ad Content Display */}
                    {previewMode === 'content' ? (
                      <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border">
                        <pre className="whitespace-pre-wrap text-sm text-gray-800 font-medium leading-relaxed">
                          {ad.content}
                        </pre>
                      </div>
                    ) : (
                      <div className="flex justify-center p-6 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border">
                        <AdPreview ad={ad} />
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 pt-2">
                      <div className="flex items-center gap-2 flex-wrap">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(ad.content)}
                          className="hover:bg-blue-50 hover:border-blue-300"
                        >
                          <Copy className="h-4 w-4 mr-1" />
                          Copy
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => shareAd(ad)}
                          className="hover:bg-green-50 hover:border-green-300"
                        >
                          <Share2 className="h-4 w-4 mr-1" />
                          Share
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="hover:bg-purple-50 hover:border-purple-300">
                              <Download className="h-4 w-4 mr-1" />
                              Export
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem onClick={() => downloadAd(ad, 'txt')}>
                              <FileText className="h-4 w-4 mr-2" />
                              Download as TXT
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => downloadAd(ad, 'json')}>
                              <FileText className="h-4 w-4 mr-2" />
                              Download as JSON
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => downloadAd(ad, 'csv')}>
                              <FileText className="h-4 w-4 mr-2" />
                              Download as CSV
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={generateAds}
                        className="text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                      >
                        <RefreshCw className="h-4 w-4 mr-1" />
                        Regenerate
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <DashboardFooter />
    </div>
  );
}
